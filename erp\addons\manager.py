#!/usr/bin/env python3
"""
Enhanced addon manager with lifecycle management and robust dependency checking
"""
import os
import sys
import json
import shutil
import importlib
import importlib.util
from typing import Dict, List, Optional, Set, Tuple, Any
from enum import Enum
from dataclasses import dataclass
from datetime import datetime

from .manifest import AddonManifest
from .async_loader import Async<PERSON>ddonLoader
from ..config import config


class AddonState(Enum):
    """Addon installation states"""
    UNINSTALLED = "uninstalled"
    INSTALLED = "installed"
    TO_INSTALL = "to_install"
    TO_UPGRADE = "to_upgrade"
    TO_REMOVE = "to_remove"
    BROKEN = "broken"


@dataclass
class AddonInfo:
    """Enhanced addon information"""
    name: str
    manifest: AddonManifest
    state: AddonState
    installed_version: Optional[str] = None
    available_version: Optional[str] = None
    install_date: Optional[datetime] = None
    dependencies: List[str] = None
    dependents: List[str] = None
    
    def __post_init__(self):
        if self.dependencies is None:
            self.dependencies = self.manifest.depends.copy()
        if self.dependents is None:
            self.dependents = []


class DependencyError(Exception):
    """Exception raised for dependency-related errors"""
    pass


class CircularDependencyError(DependencyError):
    """Exception raised for circular dependencies"""
    pass


class MissingDependencyError(DependencyError):
    """Exception raised for missing dependencies"""
    pass


class AddonManager:
    """Enhanced addon manager with lifecycle management"""
    
    def __init__(self, addons_path: str = None):
        self.addons_path = addons_path or config.addons_path
        self.loader = AsyncAddonLoader()
        # Override loader's addons path if custom path provided
        if addons_path:
            self.loader.addons_path = addons_path
        self.state_file = os.path.join(self.addons_path, '.addon_state.json')
        self._addon_states: Dict[str, AddonInfo] = {}
        self._dependency_graph: Dict[str, Set[str]] = {}
        self._reverse_dependency_graph: Dict[str, Set[str]] = {}
        self._load_addon_states()
    
    def _load_addon_states(self):
        """Load addon states from persistent storage"""
        if os.path.exists(self.state_file):
            try:
                with open(self.state_file, 'r') as f:
                    data = json.load(f)
                    for name, state_data in data.items():
                        # Convert state data back to AddonInfo
                        manifest_path = state_data.get('manifest_path')
                        if manifest_path and os.path.exists(manifest_path):
                            manifest = AddonManifest(os.path.dirname(manifest_path))
                            addon_info = AddonInfo(
                                name=name,
                                manifest=manifest,
                                state=AddonState(state_data.get('state', 'uninstalled')),
                                installed_version=state_data.get('installed_version'),
                                available_version=state_data.get('available_version'),
                                install_date=datetime.fromisoformat(state_data['install_date']) if state_data.get('install_date') else None,
                                dependencies=state_data.get('dependencies', []),
                                dependents=state_data.get('dependents', [])
                            )
                            self._addon_states[name] = addon_info
            except Exception as e:
                print(f"Warning: Could not load addon states: {e}")
    
    def _save_addon_states(self):
        """Save addon states to persistent storage"""
        try:
            data = {}
            for name, addon_info in self._addon_states.items():
                data[name] = {
                    'state': addon_info.state.value,
                    'installed_version': addon_info.installed_version,
                    'available_version': addon_info.available_version,
                    'install_date': addon_info.install_date.isoformat() if addon_info.install_date else None,
                    'dependencies': addon_info.dependencies,
                    'dependents': addon_info.dependents,
                    'manifest_path': os.path.join(addon_info.manifest.addon_path, '__manifest__.py')
                }
            
            with open(self.state_file, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            print(f"Warning: Could not save addon states: {e}")
    
    def discover_addons(self) -> Dict[str, AddonInfo]:
        """Discover all available addons and update states"""
        discovered = self.loader.discover_addons()
        
        # Update addon states with discovered addons
        for name, manifest in discovered.items():
            if name not in self._addon_states:
                # New addon discovered
                self._addon_states[name] = AddonInfo(
                    name=name,
                    manifest=manifest,
                    state=AddonState.UNINSTALLED,
                    available_version=manifest.version
                )
            else:
                # Update existing addon info
                addon_info = self._addon_states[name]
                addon_info.manifest = manifest
                addon_info.available_version = manifest.version
                
                # Check if upgrade is needed
                if (addon_info.state == AddonState.INSTALLED and 
                    addon_info.installed_version != manifest.version):
                    addon_info.state = AddonState.TO_UPGRADE
        
        # Build dependency graphs
        self._build_dependency_graphs()
        
        return self._addon_states.copy()
    
    def _build_dependency_graphs(self):
        """Build forward and reverse dependency graphs"""
        self._dependency_graph = {}
        self._reverse_dependency_graph = {}
        
        for name, addon_info in self._addon_states.items():
            self._dependency_graph[name] = set(addon_info.dependencies)
            
            # Build reverse dependencies
            for dep in addon_info.dependencies:
                if dep not in self._reverse_dependency_graph:
                    self._reverse_dependency_graph[dep] = set()
                self._reverse_dependency_graph[dep].add(name)
        
        # Update dependents in addon info
        for name, addon_info in self._addon_states.items():
            addon_info.dependents = list(self._reverse_dependency_graph.get(name, set()))
    
    def check_dependencies(self, addon_name: str) -> Tuple[bool, List[str]]:
        """Check if addon dependencies are satisfied"""
        if addon_name not in self._addon_states:
            return False, [f"Addon {addon_name} not found"]
        
        addon_info = self._addon_states[addon_name]
        missing_deps = []
        
        for dep in addon_info.dependencies:
            if dep not in self._addon_states:
                missing_deps.append(f"Dependency {dep} not found")
            elif self._addon_states[dep].state not in [AddonState.INSTALLED, AddonState.TO_UPGRADE]:
                missing_deps.append(f"Dependency {dep} not installed")
        
        return len(missing_deps) == 0, missing_deps
    
    def detect_circular_dependencies(self, addon_names: List[str] = None) -> List[List[str]]:
        """Detect circular dependencies in addon graph"""
        if addon_names is None:
            addon_names = list(self._addon_states.keys())
        
        def dfs(node: str, path: List[str], visited: Set[str], rec_stack: Set[str]) -> List[List[str]]:
            visited.add(node)
            rec_stack.add(node)
            path.append(node)
            cycles = []
            
            for neighbor in self._dependency_graph.get(node, set()):
                if neighbor in addon_names:  # Only check specified addons
                    if neighbor in rec_stack:
                        # Found a cycle
                        cycle_start = path.index(neighbor)
                        cycles.append(path[cycle_start:] + [neighbor])
                    elif neighbor not in visited:
                        cycles.extend(dfs(neighbor, path.copy(), visited, rec_stack))
            
            rec_stack.remove(node)
            return cycles
        
        visited = set()
        all_cycles = []
        
        for addon_name in addon_names:
            if addon_name not in visited:
                cycles = dfs(addon_name, [], visited, set())
                all_cycles.extend(cycles)
        
        return all_cycles
    
    def get_install_order(self, addon_names: List[str]) -> List[str]:
        """Get installation order for addons, respecting dependencies"""
        # Check for circular dependencies first
        cycles = self.detect_circular_dependencies(addon_names)
        if cycles:
            raise CircularDependencyError(f"Circular dependencies detected: {cycles}")
        
        # Check for missing dependencies
        all_deps = set()
        for addon_name in addon_names:
            if addon_name in self._addon_states:
                all_deps.update(self._addon_states[addon_name].dependencies)
        
        missing_deps = all_deps - set(self._addon_states.keys())
        if missing_deps:
            raise MissingDependencyError(f"Missing dependencies: {missing_deps}")
        
        # Topological sort
        def topological_sort(nodes: Set[str]) -> List[str]:
            visited = set()
            temp_visited = set()
            result = []
            
            def visit(node: str):
                if node in temp_visited:
                    raise CircularDependencyError(f"Circular dependency involving {node}")
                if node in visited:
                    return
                
                temp_visited.add(node)
                for dep in self._dependency_graph.get(node, set()):
                    if dep in nodes:  # Only consider nodes we're installing
                        visit(dep)
                temp_visited.remove(node)
                visited.add(node)
                result.append(node)
            
            for node in nodes:
                if node not in visited:
                    visit(node)
            
            return result
        
        # Include all dependencies in the sort
        all_nodes = set(addon_names)
        for addon_name in addon_names:
            if addon_name in self._addon_states:
                all_nodes.update(self._addon_states[addon_name].dependencies)
        
        # Filter to only include available addons
        available_nodes = {name for name in all_nodes if name in self._addon_states}
        
        return topological_sort(available_nodes)

    def install_addon(self, addon_name: str, force: bool = False) -> bool:
        """Install a single addon"""
        if addon_name not in self._addon_states:
            print(f"Addon {addon_name} not found")
            return False

        addon_info = self._addon_states[addon_name]

        # Check if already installed
        if addon_info.state == AddonState.INSTALLED and not force:
            print(f"Addon {addon_name} is already installed")
            return True

        # Check if installable
        if not addon_info.manifest.installable:
            print(f"Addon {addon_name} is not installable")
            return False

        # Check dependencies
        deps_ok, dep_errors = self.check_dependencies(addon_name)
        if not deps_ok and not force:
            print(f"Cannot install {addon_name}: {', '.join(dep_errors)}")
            return False

        try:
            # Install dependencies first
            for dep in addon_info.dependencies:
                if dep in self._addon_states:
                    dep_info = self._addon_states[dep]
                    if dep_info.state != AddonState.INSTALLED:
                        if not self.install_addon(dep, force):
                            print(f"Failed to install dependency {dep}")
                            return False

            # Load the addon
            if not self.loader.load_addon(addon_name):
                print(f"Failed to load addon {addon_name}")
                return False

            # Update state
            addon_info.state = AddonState.INSTALLED
            addon_info.installed_version = addon_info.available_version
            addon_info.install_date = datetime.now()

            # Save state
            self._save_addon_states()

            print(f"Successfully installed addon: {addon_name}")
            return True

        except Exception as e:
            print(f"Error installing addon {addon_name}: {e}")
            addon_info.state = AddonState.BROKEN
            self._save_addon_states()
            return False

    def uninstall_addon(self, addon_name: str, force: bool = False) -> bool:
        """Uninstall a single addon"""
        if addon_name not in self._addon_states:
            print(f"Addon {addon_name} not found")
            return False

        addon_info = self._addon_states[addon_name]

        # Check if installed
        if addon_info.state != AddonState.INSTALLED:
            print(f"Addon {addon_name} is not installed")
            return True

        # Check dependents
        if addon_info.dependents and not force:
            installed_dependents = []
            for dependent in addon_info.dependents:
                if (dependent in self._addon_states and
                    self._addon_states[dependent].state == AddonState.INSTALLED):
                    installed_dependents.append(dependent)

            if installed_dependents:
                print(f"Cannot uninstall {addon_name}: required by {', '.join(installed_dependents)}")
                return False

        try:
            # Uninstall dependents first if force
            if force:
                for dependent in addon_info.dependents:
                    if (dependent in self._addon_states and
                        self._addon_states[dependent].state == AddonState.INSTALLED):
                        self.uninstall_addon(dependent, force=True)

            # Remove from loader
            if addon_name in self.loader.loaded_addons:
                del self.loader.loaded_addons[addon_name]
            if addon_name in self.loader.addon_modules:
                del self.loader.addon_modules[addon_name]

            # Update state
            addon_info.state = AddonState.UNINSTALLED
            addon_info.installed_version = None
            addon_info.install_date = None

            # Save state
            self._save_addon_states()

            print(f"Successfully uninstalled addon: {addon_name}")
            return True

        except Exception as e:
            print(f"Error uninstalling addon {addon_name}: {e}")
            return False

    def upgrade_addon(self, addon_name: str, force: bool = False) -> bool:
        """Upgrade a single addon"""
        if addon_name not in self._addon_states:
            print(f"Addon {addon_name} not found")
            return False

        addon_info = self._addon_states[addon_name]

        # Check if installed
        if addon_info.state != AddonState.INSTALLED:
            print(f"Addon {addon_name} is not installed")
            return False

        # Check if upgrade is needed
        if addon_info.installed_version == addon_info.available_version and not force:
            print(f"Addon {addon_name} is already up to date")
            return True

        try:
            # Uninstall current version
            if not self.uninstall_addon(addon_name, force=True):
                print(f"Failed to uninstall current version of {addon_name}")
                return False

            # Install new version
            if not self.install_addon(addon_name, force):
                print(f"Failed to install new version of {addon_name}")
                return False

            print(f"Successfully upgraded addon: {addon_name}")
            return True

        except Exception as e:
            print(f"Error upgrading addon {addon_name}: {e}")
            return False

    def get_addon_info(self, addon_name: str) -> Optional[AddonInfo]:
        """Get detailed information about an addon"""
        return self._addon_states.get(addon_name)

    def list_addons(self, state_filter: AddonState = None) -> Dict[str, AddonInfo]:
        """List addons, optionally filtered by state"""
        if state_filter is None:
            return self._addon_states.copy()

        return {name: info for name, info in self._addon_states.items()
                if info.state == state_filter}

    def get_dependency_tree(self, addon_name: str, max_depth: int = 10) -> Dict[str, Any]:
        """Get dependency tree for an addon"""
        if addon_name not in self._addon_states:
            return {}

        def build_tree(name: str, depth: int = 0) -> Dict[str, Any]:
            if depth > max_depth:
                return {"name": name, "max_depth_reached": True}

            addon_info = self._addon_states.get(name)
            if not addon_info:
                return {"name": name, "not_found": True}

            tree = {
                "name": name,
                "state": addon_info.state.value,
                "version": addon_info.installed_version or addon_info.available_version,
                "dependencies": []
            }

            for dep in addon_info.dependencies:
                tree["dependencies"].append(build_tree(dep, depth + 1))

            return tree

        return build_tree(addon_name)

    def validate_addon_integrity(self, addon_name: str) -> Tuple[bool, List[str]]:
        """Validate addon integrity and configuration"""
        if addon_name not in self._addon_states:
            return False, [f"Addon {addon_name} not found"]

        addon_info = self._addon_states[addon_name]
        errors = []

        # Check if addon directory exists
        if not os.path.exists(addon_info.manifest.addon_path):
            errors.append(f"Addon directory not found: {addon_info.manifest.addon_path}")

        # Check if manifest file exists
        manifest_file = os.path.join(addon_info.manifest.addon_path, '__manifest__.py')
        if not os.path.exists(manifest_file):
            errors.append(f"Manifest file not found: {manifest_file}")

        # Check if __init__.py exists
        init_file = os.path.join(addon_info.manifest.addon_path, '__init__.py')
        if not os.path.exists(init_file):
            errors.append(f"__init__.py file not found: {init_file}")

        # Check dependencies
        for dep in addon_info.dependencies:
            if dep not in self._addon_states:
                errors.append(f"Dependency {dep} not available")

        return len(errors) == 0, errors

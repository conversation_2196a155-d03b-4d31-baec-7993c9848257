"""
Model and field definition models for base addon
"""
import sys
import os

# Add the ERP core to Python path
erp_path = os.path.join(os.path.dirname(__file__), '..', '..', '..')
if erp_path not in sys.path:
    sys.path.insert(0, erp_path)

# Import base model - use sync version for compatibility
from erp.models.base import BaseModel
from erp.fields import Char, Text, Boolean, Integer, Selection

# Ensure this addon is available via standardized import
from erp.addons import ensure_addon_import
addon_path = os.path.join(os.path.dirname(__file__), '..')
ensure_addon_import('base', addon_path)


class IrModel(BaseModel):
    """Model for storing model definitions"""

    _name = 'ir.model'
    _description = 'Model'
    _table = 'ir_model'

    # Override name field to be more specific
    name = Char(string='Model Name', required=True, help='Technical name of the model (e.g., res.partner)')
    model = Char(string='Model', required=True, help='Model technical name')
    info = Text(string='Information', help='Model description')
    description = Text(string='Description', help='Model description (alias for info)')
    table = Char(string='Table Name', help='Database table name')
    state = Selection([
        ('manual', 'Custom Object'),
        ('base', 'Base Object'),
    ], string='Type', default='manual', required=True)

    transient = Boolean(string='Transient Model', default=False,
                       help='Whether this model is transient (temporary)')

    def _get_model_class(self):
        """Get the actual model class for this model"""
        from erp.models.base import ModelRegistry
        return ModelRegistry.get(self.model)


class IrModelFields(BaseModel):
    """Model for storing field definitions"""

    _name = 'ir.model.fields'
    _description = 'Model Fields'
    _table = 'ir_model_fields'

    # Override name field to be more specific
    name = Char(string='Field Name', required=True, help='Technical name of the field')
    field_description = Char(string='Field Label', required=True, help='Human readable label')
    help = Text(string='Field Help', help='Help text for the field')
    model = Char(string='Model', required=True, help='Model technical name')
    model_id = Char(string='Model ID', help='Reference to ir.model record')

    ttype = Selection([
        ('char', 'Char'),
        ('text', 'Text'),
        ('integer', 'Integer'),
        ('float', 'Float'),
        ('boolean', 'Boolean'),
        ('date', 'Date'),
        ('datetime', 'Datetime'),
        ('selection', 'Selection'),
        ('many2one', 'Many2one'),
        ('one2many', 'One2many'),
        ('many2many', 'Many2many'),
    ], string='Field Type', required=True)

    required = Boolean(string='Required', default=False)
    readonly = Boolean(string='Readonly', default=False)
    index = Boolean(string='Indexed', default=False)

    size = Integer(string='Size', help='Field size (for char fields)')

    relation = Char(string='Relation', help='Related model for relational fields')
    relation_field = Char(string='Relation Field', help='Field name in related model')

    selection = Text(string='Selection Options', help='Selection options as string representation')

    state = Selection([
        ('manual', 'Custom Field'),
        ('base', 'Base Field'),
    ], string='Type', default='manual', required=True)

    def _get_field_definition(self):
        """Get field definition for this field"""
        from erp.fields import (
            Char, Text, Integer, Float, Boolean, Date, Datetime,
            Selection, Many2one, One2many, Many2many
        )

        field_classes = {
            'char': Char,
            'text': Text,
            'integer': Integer,
            'float': Float,
            'boolean': Boolean,
            'date': Date,
            'datetime': Datetime,
            'selection': Selection,
            'many2one': Many2one,
            'one2many': One2many,
            'many2many': Many2many,
        }

        field_class = field_classes.get(self.ttype)
        if not field_class:
            return None

        kwargs = {
            'string': self.field_description,
            'required': self.required,
            'readonly': self.readonly,
            'help': self.help,
            'index': self.index,
        }

        # Add type-specific parameters
        if self.ttype == 'char' and self.size:
            kwargs['size'] = self.size
        elif self.ttype == 'selection' and self.selection:
            # Parse selection string (simplified)
            kwargs['selection'] = eval(self.selection) if self.selection else []
        elif self.ttype in ('many2one', 'one2many', 'many2many') and self.relation:
            kwargs['comodel_name'] = self.relation
            if self.ttype == 'one2many' and self.relation_field:
                kwargs['inverse_name'] = self.relation_field

        return field_class(**kwargs)

# The models are automatically registered via the metaclass

["tests/integration/test_addon_loading.py::TestAddonLoading::test_addon_auto_install_flag", "tests/integration/test_addon_loading.py::TestAddonLoading::test_addon_dependency_resolution", "tests/integration/test_addon_loading.py::TestAddonLoading::test_addon_discovery", "tests/integration/test_addon_loading.py::TestAddonLoading::test_addon_loading_with_models", "tests/integration/test_addon_loading.py::TestAddonLoading::test_addon_manifest_loading", "tests/integration/test_addon_loading.py::TestAddonLoading::test_addon_uninstallable_flag", "tests/integration/test_addon_loading.py::TestAddonLoading::test_circular_dependency_detection", "tests/integration/test_addon_loading.py::TestAddonLoading::test_missing_dependency_handling", "tests/test_fields.py::TestBaseField::test_default_value", "tests/test_fields.py::TestBaseField::test_field_creation", "tests/test_fields.py::TestBaseField::test_validation_optional", "tests/test_fields.py::TestBaseField::test_validation_required", "tests/test_fields.py::TestBooleanField::test_boolean_validation", "tests/test_fields.py::TestCharField::test_char_creation", "tests/test_fields.py::TestCharField::test_char_no_size", "tests/test_fields.py::TestCharField::test_char_validation", "tests/test_fields.py::TestDateField::test_date_validation", "tests/test_fields.py::TestDatetimeField::test_datetime_validation", "tests/test_fields.py::TestFloatField::test_float_sql_type", "tests/test_fields.py::TestFloatField::test_float_validation", "tests/test_fields.py::TestHtmlField::test_html_validation", "tests/test_fields.py::TestIntegerField::test_integer_validation", "tests/test_fields.py::TestJsonField::test_json_validation", "tests/test_fields.py::TestMany2oneField::test_many2one_validation", "tests/test_fields.py::TestSelectionField::test_dynamic_selection", "tests/test_fields.py::TestSelectionField::test_selection_items", "tests/test_fields.py::TestSelectionField::test_selection_validation", "tests/test_schema_utils.py::TestSchemaComparator::test_compare_model_with_table_missing_model", "tests/test_schema_utils.py::TestSchemaComparator::test_default_value_handling", "tests/test_schema_utils.py::TestSchemaComparator::test_field_type_mapping", "tests/test_schema_utils.py::TestSchemaComparator::test_types_compatible", "tests/test_schema_utils.py::TestSchemaGenerator::test_default_value_handling", "tests/test_schema_utils.py::TestSchemaGenerator::test_field_type_mapping", "tests/test_schema_utils.py::TestSchemaGenerator::test_generate_create_table_sql_async", "tests/test_schema_utils.py::TestSchemaGenerator::test_generate_create_table_sql_sync", "tests/test_schema_utils.py::TestSchemaGenerator::test_get_all_models_schema", "tests/test_schema_utils.py::TestSchemaGenerator::test_get_model_schema_async", "tests/test_schema_utils.py::TestSchemaGenerator::test_get_model_schema_nonexistent", "tests/test_schema_utils.py::TestSchemaGenerator::test_get_model_schema_sync", "tests/test_schema_utils.py::TestSchemaGenerator::test_schema_generator_integration", "tests/unit/test_addon_manager.py::TestAddonManager::test_addon_installation", "tests/unit/test_addon_manager.py::TestAddonManager::test_addon_installation_with_dependencies", "tests/unit/test_addon_manager.py::TestAddonManager::test_addon_integrity_validation", "tests/unit/test_addon_manager.py::TestAddonManager::test_addon_manager_creation", "tests/unit/test_addon_manager.py::TestAddonManager::test_addon_state_persistence", "tests/unit/test_addon_manager.py::TestAddonManager::test_addon_uninstallation", "tests/unit/test_addon_manager.py::TestAddonManager::test_addon_uninstallation_with_dependents", "tests/unit/test_addon_manager.py::TestAddonManager::test_addon_upgrade", "tests/unit/test_addon_manager.py::TestAddonManager::test_circular_dependency_detection", "tests/unit/test_addon_manager.py::TestAddonManager::test_dependency_checking", "tests/unit/test_addon_manager.py::TestAddonManager::test_dependency_tree", "tests/unit/test_addon_manager.py::TestAddonManager::test_discover_addons", "tests/unit/test_addon_manager.py::TestAddonManager::test_install_order_calculation", "tests/unit/test_config.py::TestConfig::test_addons_path_property", "tests/unit/test_config.py::TestConfig::test_config_creation", "tests/unit/test_config.py::TestConfig::test_config_file_loading", "tests/unit/test_config.py::TestConfig::test_config_set_get", "tests/unit/test_config.py::TestConfig::test_db_config_property", "tests/unit/test_config.py::TestConfig::test_default_values", "tests/unit/test_config.py::TestConfig::test_invalid_config_file", "tests/unit/test_config.py::TestConfig::test_missing_config_file", "tests/unit/test_config.py::TestConfig::test_server_config_property", "tests/unit/test_fields.py::TestBaseField::test_default_value", "tests/unit/test_fields.py::TestBaseField::test_field_creation", "tests/unit/test_fields.py::TestBaseField::test_validation_optional", "tests/unit/test_fields.py::TestBaseField::test_validation_required", "tests/unit/test_fields.py::TestBooleanField::test_boolean_validation", "tests/unit/test_fields.py::TestCharField::test_char_creation", "tests/unit/test_fields.py::TestCharField::test_char_no_size", "tests/unit/test_fields.py::TestCharField::test_char_validation", "tests/unit/test_fields.py::TestDateField::test_date_validation", "tests/unit/test_fields.py::TestDatetimeField::test_datetime_validation", "tests/unit/test_fields.py::TestFloatField::test_float_sql_type", "tests/unit/test_fields.py::TestFloatField::test_float_validation", "tests/unit/test_fields.py::TestHtmlField::test_html_validation", "tests/unit/test_fields.py::TestIntegerField::test_integer_validation", "tests/unit/test_fields.py::TestJsonField::test_json_validation", "tests/unit/test_fields.py::TestSelectionField::test_dynamic_selection", "tests/unit/test_fields.py::TestSelectionField::test_selection_items", "tests/unit/test_fields.py::TestSelectionField::test_selection_validation", "tests/unit/test_models.py::TestBaseModel::test_field_access", "tests/unit/test_models.py::TestBaseModel::test_field_defaults", "tests/unit/test_models.py::TestBaseModel::test_model_creation", "tests/unit/test_models.py::TestBaseModel::test_model_string_representation", "tests/unit/test_models.py::TestBaseModel::test_read_method", "tests/unit/test_models.py::TestBaseModel::test_required_fields", "tests/unit/test_models.py::TestBaseModel::test_write_method", "tests/unit/test_models.py::TestModelMeta::test_field_collection", "tests/unit/test_models.py::TestModelMeta::test_model_registration", "tests/unit/test_models.py::TestModelRegistry::test_registry_isolation", "tests/unit/test_models.py::TestModelRegistry::test_registry_operations"]
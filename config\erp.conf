[options]
# Server configuration
http_port = 8069
http_interface = 127.0.0.1

# Database configuration
db_host = localhost
db_port = 5432
db_user = erp
db_password = erp

# Database mode configuration
# For SINGLE DATABASE mode: Set db_name to a specific database
# For MULTI-DATABASE mode: Leave db_name empty or comment it out
db_name = erp_db

# Multi-database support (only used when db_name is not set)
# When db_name is empty, the system will work in multi-database mode
# and use db_filter to determine which databases are accessible
list_db = True
db_filter = ^erp_.*

# Connection pooling
db_pool_min_size = 10
db_pool_max_size = 20

# Addons path
addons_path = addons

# Logging
log_level = info
log_file = erp.log

# Security
admin_passwd = admin

# Server type (asgi or wsgi)
server_type = asgi

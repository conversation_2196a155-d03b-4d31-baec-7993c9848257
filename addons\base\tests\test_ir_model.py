"""
Tests for ir.model and ir.model.fields models in base addon
Demonstrates testing model definitions and field management
"""
import sys
import os

# Add ERP core to path for testing
erp_path = os.path.join(os.path.dirname(__file__), '..', '..', '..')
if erp_path not in sys.path:
    sys.path.insert(0, erp_path)

from erp.tests.common import TransactionCase
from erp.tests.tags import tag, Tags
from erp.addons import get_addon_module


@tag(Tags.UNIT, Tags.BASE, 'model_management')
class TestIrModel(TransactionCase):
    """Test cases for ir.model model"""
    
    def setUp(self):
        """Set up test case"""
        super().setUp()
        
        # Ensure base addon is loaded
        base_addon = get_addon_module('base')
        self.assertIsNotNone(base_addon, "Base addon should be available")
        
        # Get the model classes
        self.IrModel = self.env['ir.model']
        self.IrModelFields = self.env['ir.model.fields']
    
    def test_create_model_definition(self):
        """Test creating a model definition"""
        model_data = {
            'name': 'test.model',
            'model': 'test.model',
            'description': 'Test Model',
            'table': 'test_model',
            'state': 'manual'
        }
        
        model = self.IrModel.create(model_data)
        
        self.assertEqual(model.name, 'test.model')
        self.assertEqual(model.model, 'test.model')
        self.assertEqual(model.description, 'Test Model')
        self.assertEqual(model.table, 'test_model')
        self.assertEqual(model.state, 'manual')
    
    def test_model_name_required(self):
        """Test that model name is required"""
        with self.assertRaises(Exception):
            self.IrModel.create({
                'description': 'Test Model',
                'state': 'manual'
            })
    
    def test_model_table_generation(self):
        """Test automatic table name generation"""
        model = self.IrModel.create({
            'name': 'test.auto.table',
            'model': 'test.auto.table',
            'description': 'Test Auto Table Model',
            'state': 'manual'
        })
        
        # Table name should be auto-generated from model name
        expected_table = 'test_auto_table'
        self.assertEqual(model.table, expected_table)
    
    def test_model_state_values(self):
        """Test valid model state values"""
        valid_states = ['manual', 'base']
        
        for state in valid_states:
            model = self.IrModel.create({
                'name': f'test.model.{state}',
                'model': f'test.model.{state}',
                'description': f'Test Model {state}',
                'state': state
            })
            self.assertEqual(model.state, state)


@tag(Tags.UNIT, Tags.BASE, 'field_management')
class TestIrModelFields(TransactionCase):
    """Test cases for ir.model.fields model"""
    
    def setUp(self):
        """Set up test case"""
        super().setUp()
        
        # Get the model classes
        self.IrModel = self.env['ir.model']
        self.IrModelFields = self.env['ir.model.fields']
        
        # Create a test model for field testing
        self.test_model = self.IrModel.create({
            'name': 'test.field.model',
            'model': 'test.field.model',
            'description': 'Test Field Model',
            'state': 'manual'
        })
    
    def test_create_char_field(self):
        """Test creating a Char field"""
        field_data = {
            'name': 'test_char',
            'field_description': 'Test Char Field',
            'model_id': self.test_model.id,
            'ttype': 'char',
            'size': 100,
            'required': True
        }
        
        field = self.IrModelFields.create(field_data)
        
        self.assertEqual(field.name, 'test_char')
        self.assertEqual(field.field_description, 'Test Char Field')
        self.assertEqual(field.ttype, 'char')
        self.assertEqual(field.size, 100)
        self.assertTrue(field.required)
    
    def test_create_integer_field(self):
        """Test creating an Integer field"""
        field_data = {
            'name': 'test_integer',
            'field_description': 'Test Integer Field',
            'model_id': self.test_model.id,
            'ttype': 'integer',
            'required': False
        }
        
        field = self.IrModelFields.create(field_data)
        
        self.assertEqual(field.name, 'test_integer')
        self.assertEqual(field.ttype, 'integer')
        self.assertFalse(field.required)
    
    def test_create_boolean_field(self):
        """Test creating a Boolean field"""
        field_data = {
            'name': 'test_boolean',
            'field_description': 'Test Boolean Field',
            'model_id': self.test_model.id,
            'ttype': 'boolean'
        }
        
        field = self.IrModelFields.create(field_data)
        
        self.assertEqual(field.name, 'test_boolean')
        self.assertEqual(field.ttype, 'boolean')
    
    def test_create_text_field(self):
        """Test creating a Text field"""
        field_data = {
            'name': 'test_text',
            'field_description': 'Test Text Field',
            'model_id': self.test_model.id,
            'ttype': 'text'
        }
        
        field = self.IrModelFields.create(field_data)
        
        self.assertEqual(field.name, 'test_text')
        self.assertEqual(field.ttype, 'text')
    
    def test_create_selection_field(self):
        """Test creating a Selection field"""
        field_data = {
            'name': 'test_selection',
            'field_description': 'Test Selection Field',
            'model_id': self.test_model.id,
            'ttype': 'selection',
            'selection': "[('option1', 'Option 1'), ('option2', 'Option 2')]"
        }
        
        field = self.IrModelFields.create(field_data)
        
        self.assertEqual(field.name, 'test_selection')
        self.assertEqual(field.ttype, 'selection')
        self.assertIsNotNone(field.selection)
    
    def test_field_name_required(self):
        """Test that field name is required"""
        with self.assertRaises(Exception):
            self.IrModelFields.create({
                'field_description': 'Test Field',
                'model_id': self.test_model.id,
                'ttype': 'char'
            })
    
    def test_field_type_required(self):
        """Test that field type is required"""
        with self.assertRaises(Exception):
            self.IrModelFields.create({
                'name': 'test_field',
                'field_description': 'Test Field',
                'model_id': self.test_model.id
            })
    
    def test_field_model_required(self):
        """Test that field model is required"""
        with self.assertRaises(Exception):
            self.IrModelFields.create({
                'name': 'test_field',
                'field_description': 'Test Field',
                'ttype': 'char'
            })


@tag(Tags.INTEGRATION, Tags.BASE, 'model_field_integration')
class TestModelFieldIntegration(TransactionCase):
    """Integration tests for model and field relationships"""
    
    def setUp(self):
        """Set up test case"""
        super().setUp()
        
        self.IrModel = self.env['ir.model']
        self.IrModelFields = self.env['ir.model.fields']
    
    def test_model_with_multiple_fields(self):
        """Test creating a model with multiple fields"""
        # Create model
        model = self.IrModel.create({
            'name': 'test.complete.model',
            'model': 'test.complete.model',
            'description': 'Complete Test Model',
            'state': 'manual'
        })
        
        # Create fields
        fields_data = [
            {
                'name': 'name',
                'field_description': 'Name',
                'model_id': model.id,
                'ttype': 'char',
                'size': 100,
                'required': True
            },
            {
                'name': 'description',
                'field_description': 'Description',
                'model_id': model.id,
                'ttype': 'text'
            },
            {
                'name': 'active',
                'field_description': 'Active',
                'model_id': model.id,
                'ttype': 'boolean'
            },
            {
                'name': 'priority',
                'field_description': 'Priority',
                'model_id': model.id,
                'ttype': 'integer'
            }
        ]
        
        created_fields = []
        for field_data in fields_data:
            field = self.IrModelFields.create(field_data)
            created_fields.append(field)
        
        # Verify all fields are created and linked to model
        self.assertEqual(len(created_fields), 4)
        for field in created_fields:
            self.assertEqual(field.model_id.id, model.id)
        
        # Verify field names
        field_names = [field.name for field in created_fields]
        expected_names = ['name', 'description', 'active', 'priority']
        self.assertEqual(sorted(field_names), sorted(expected_names))

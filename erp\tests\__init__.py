"""
ERP Testing Framework
Provides Odoo-like testing infrastructure with TransactionCase and SingleTransactionCase
"""
from .common import TransactionCase, SingleTransactionCase, TestCase
from .runner import TestRunner, TestSuite
from .tags import tag, tagged

__all__ = [
    'TransactionCase', 
    'SingleTransactionCase', 
    'TestCase',
    'TestRunner',
    'TestSuite',
    'tag',
    'tagged'
]

#!/usr/bin/env python3
"""
ERP System Command Line Interface
Main entry point for server management, addon operations, and testing
"""
import sys
import os
import argparse

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(__file__))

from erp.config import config
from erp.server import create_app
from erp.addons.manager import AddonManager, AddonState


class ERPCommandLine:
    """ERP Command Line Interface"""
    
    def __init__(self):
        self.parser = self._create_parser()
    
    def _create_parser(self):
        """Create argument parser"""
        parser = argparse.ArgumentParser(
            description='ERP System Command Line Interface',
            prog='erp-bin'
        )
        
        # Global options
        parser.add_argument('--config', '-c', help='Configuration file path')
        parser.add_argument('--db-name', help='Database name')
        parser.add_argument('--addons-path', help='Addons path')
        parser.add_argument('--verbose', '-v', action='store_true', help='Verbose output')
        
        # Subcommands
        subparsers = parser.add_subparsers(dest='command', help='Available commands')
        
        # Server commands
        self._add_server_commands(subparsers)
        
        # Addon commands
        self._add_addon_commands(subparsers)
        
        # Database commands
        self._add_database_commands(subparsers)
        
        # Test commands
        self._add_test_commands(subparsers)
        
        return parser
    
    def _add_server_commands(self, subparsers):
        """Add server-related commands"""
        # Start server
        start_parser = subparsers.add_parser('start', help='Start ERP ASGI server')
        start_parser.add_argument('--host', default='127.0.0.1', help='Server host')
        start_parser.add_argument('--port', type=int, default=8069, help='Server port')
        start_parser.add_argument('--debug', action='store_true', help='Debug mode')
        start_parser.add_argument('--reload', action='store_true', help='Auto-reload')
        start_parser.add_argument('--workers', type=int, default=1, help='Worker processes')

        # Stop server
        subparsers.add_parser('stop', help='Stop ERP server')

        # Server status
        subparsers.add_parser('status', help='Show server status')
    
    def _add_addon_commands(self, subparsers):
        """Add addon-related commands"""
        # List addons
        list_parser = subparsers.add_parser('list', help='List addons')
        list_parser.add_argument('--installed', action='store_true', help='Show only installed addons')
        list_parser.add_argument('--available', action='store_true', help='Show only available addons')
        
        # Install addon
        install_parser = subparsers.add_parser('install', help='Install addon')
        install_parser.add_argument('addon_name', help='Addon name to install')
        install_parser.add_argument('--force', action='store_true', help='Force installation')
        
        # Uninstall addon
        uninstall_parser = subparsers.add_parser('uninstall', help='Uninstall addon')
        uninstall_parser.add_argument('addon_name', help='Addon name to uninstall')
        uninstall_parser.add_argument('--force', action='store_true', help='Force uninstallation')
        
        # Upgrade addon
        upgrade_parser = subparsers.add_parser('upgrade', help='Upgrade addon')
        upgrade_parser.add_argument('addon_name', nargs='?', help='Addon name to upgrade (all if not specified)')
        upgrade_parser.add_argument('--all', action='store_true', help='Upgrade all addons')
        
        # Addon info
        info_parser = subparsers.add_parser('info', help='Show addon information')
        info_parser.add_argument('addon_name', help='Addon name')
    
    def _add_database_commands(self, subparsers):
        """Add database-related commands"""
        # Create database
        create_db_parser = subparsers.add_parser('create-db', help='Create database')
        create_db_parser.add_argument('db_name', help='Database name')
        create_db_parser.add_argument('--demo', action='store_true', help='Install demo data')
        
        # Drop database
        drop_db_parser = subparsers.add_parser('drop-db', help='Drop database')
        drop_db_parser.add_argument('db_name', help='Database name')
        drop_db_parser.add_argument('--force', action='store_true', help='Force drop')
        
        # List databases
        subparsers.add_parser('list-db', help='List databases')
        
        # Database backup
        backup_parser = subparsers.add_parser('backup', help='Backup database')
        backup_parser.add_argument('db_name', help='Database name')
        backup_parser.add_argument('--output', '-o', help='Output file path')
        
        # Database restore
        restore_parser = subparsers.add_parser('restore', help='Restore database')
        restore_parser.add_argument('db_name', help='Database name')
        restore_parser.add_argument('backup_file', help='Backup file path')
    
    def _add_test_commands(self, subparsers):
        """Add test-related commands"""
        # Main test command with Odoo-like features
        test_parser = subparsers.add_parser('test', help='Run tests')

        # Test enabling (Odoo-style)
        test_parser.add_argument('--test-enable', action='store_true',
                               help='Enable test mode (required for running tests)')

        # Module/addon selection
        test_parser.add_argument('-i', '--install', dest='modules', action='append',
                               help='Install and test specific modules (can be used multiple times)')
        test_parser.add_argument('-u', '--update', dest='update_modules', action='append',
                               help='Update and test specific modules (can be used multiple times)')
        test_parser.add_argument('--module', '-m', help='Test specific module (alias for -i)')

        # Test filtering
        test_parser.add_argument('--test-tags', help='Run tests with specific tags (comma-separated)')
        test_parser.add_argument('--test-exclude-tags', help='Exclude tests with specific tags (comma-separated)')
        test_parser.add_argument('--pattern', '-p', help='Test name pattern to match')

        # Test frameworks
        test_parser.add_argument('--framework', choices=['pytest', 'unittest', 'erp'],
                               default='erp', help='Test framework to use')
        test_parser.add_argument('--pytest-args', help='Additional arguments to pass to pytest')
        test_parser.add_argument('--unittest-args', help='Additional arguments to pass to unittest')

        # Test types
        test_parser.add_argument('--unit', action='store_true', help='Run only unit tests')
        test_parser.add_argument('--integration', action='store_true', help='Run only integration tests')
        test_parser.add_argument('--performance', action='store_true', help='Run performance tests')
        test_parser.add_argument('--async-tests', action='store_true', help='Run async tests')
        test_parser.add_argument('--all-tests', action='store_true', help='Run all tests including slow ones')

        # Output and reporting
        test_parser.add_argument('--coverage', action='store_true', help='Generate coverage report')
        test_parser.add_argument('--coverage-html', action='store_true', help='Generate HTML coverage report')
        test_parser.add_argument('--junit-xml', help='Generate JUnit XML report to specified file')
        test_parser.add_argument('--verbose', '-v', action='count', default=1, help='Increase verbosity')
        test_parser.add_argument('--quiet', '-q', action='store_true', help='Minimal output')
        test_parser.add_argument('--list-tests', action='store_true', help='List available tests without running')

        # Test database and environment
        test_parser.add_argument('--test-db', help='Test database name (default: erp_test)')
        test_parser.add_argument('--keep-db', action='store_true', help='Keep test database after tests')
        test_parser.add_argument('--recreate-db', action='store_true', help='Recreate test database before tests')

        # Legacy test commands
        subparsers.add_parser('test-setup', help='Setup test database')
        subparsers.add_parser('test-cleanup', help='Cleanup test database')
    
    def run(self, args=None):
        """Run the command line interface"""
        args = self.parser.parse_args(args)
        
        # Apply global configuration
        self._apply_global_config(args)
        
        if not args.command:
            self.parser.print_help()
            return 1
        
        # Route to appropriate handler
        try:
            if args.command == 'start':
                return self._handle_start(args)
            elif args.command == 'stop':
                return self._handle_stop(args)
            elif args.command == 'status':
                return self._handle_status(args)
            elif args.command == 'list':
                return self._handle_list(args)
            elif args.command == 'install':
                return self._handle_install(args)
            elif args.command == 'uninstall':
                return self._handle_uninstall(args)
            elif args.command == 'upgrade':
                return self._handle_upgrade(args)
            elif args.command == 'info':
                return self._handle_info(args)
            elif args.command == 'create-db':
                return self._handle_create_db(args)
            elif args.command == 'drop-db':
                return self._handle_drop_db(args)
            elif args.command == 'list-db':
                return self._handle_list_db(args)
            elif args.command == 'backup':
                return self._handle_backup(args)
            elif args.command == 'restore':
                return self._handle_restore(args)
            elif args.command == 'test':
                return self._handle_test(args)
            elif args.command == 'test-setup':
                return self._handle_test_setup(args)
            elif args.command == 'test-cleanup':
                return self._handle_test_cleanup(args)
            else:
                print(f"Unknown command: {args.command}")
                return 1
        except KeyboardInterrupt:
            print("\nOperation cancelled by user")
            return 1
        except Exception as e:
            if args.verbose:
                import traceback
                traceback.print_exc()
            else:
                print(f"Error: {e}")
            return 1
    
    def _apply_global_config(self, args):
        """Apply global configuration options"""
        if args.config:
            config.config_file = args.config
            config._load_config()
        
        if args.db_name:
            config.set('options', 'db_name', args.db_name)
        
        if args.addons_path:
            config.set('options', 'addons_path', args.addons_path)
    
    def _handle_start(self, args):
        """Handle start command"""
        print("Starting ERP ASGI server...")
        print(f"Configuration: {config.config_file}")
        print(f"Database: {config.get('options', 'db_name')}")
        print(f"Addons path: {config.addons_path}")

        return self._start_server(args)

    def _start_server(self, args):
        """Start ASGI server"""
        try:
            import uvicorn
            app = create_app()

            uvicorn.run(
                app,
                host=args.host,
                port=args.port,
                reload=args.reload,
                workers=args.workers if not args.reload else 1,
                log_level="info",
                access_log=True
            )
            return 0
        except Exception as e:
            print(f"Failed to start server: {e}")
            return 1
    
    def _handle_stop(self, args):
        """Handle stop command"""
        print("Stop command not implemented yet")
        return 0
    
    def _handle_status(self, args):
        """Handle status command"""
        print("Status command not implemented yet")
        return 0

    def _handle_list(self, args):
        """Handle list addons command"""
        try:
            manager = AddonManager()
            addons = manager.discover_addons()

            print(f"Found {len(addons)} addons:")
            print("-" * 70)

            for name, addon_info in addons.items():
                # Status icon based on state
                status_icons = {
                    AddonState.INSTALLED: "✓",
                    AddonState.UNINSTALLED: "○",
                    AddonState.TO_INSTALL: "⏳",
                    AddonState.TO_UPGRADE: "⬆",
                    AddonState.TO_REMOVE: "⬇",
                    AddonState.BROKEN: "✗"
                }

                icon = status_icons.get(addon_info.state, "?")
                state_name = addon_info.state.value.replace('_', ' ').title()

                # Show installed vs available version
                version_info = addon_info.available_version
                if addon_info.installed_version and addon_info.installed_version != addon_info.available_version:
                    version_info = f"{addon_info.installed_version} → {addon_info.available_version}"

                print(f"{icon} {name} ({version_info}) - {state_name}")
                if addon_info.manifest.description:
                    print(f"    {addon_info.manifest.description}")
                if addon_info.dependencies:
                    print(f"    Dependencies: {', '.join(addon_info.dependencies)}")
                print()

            return 0
        except Exception as e:
            print(f"Failed to list addons: {e}")
            return 1

    def _handle_install(self, args):
        """Handle install addon command"""
        try:
            manager = AddonManager()
            manager.discover_addons()

            print(f"Installing addon: {args.addon_name}")

            # Check if addon exists
            addon_info = manager.get_addon_info(args.addon_name)
            if not addon_info:
                print(f"Addon '{args.addon_name}' not found")
                return 1

            # Check dependencies
            if not args.force:
                deps_ok, dep_errors = manager.check_dependencies(args.addon_name)
                if not deps_ok:
                    print(f"Dependency issues:")
                    for error in dep_errors:
                        print(f"  - {error}")
                    print("Use --force to install anyway")
                    return 1

            # Install addon
            success = manager.install_addon(args.addon_name, force=args.force)
            if success:
                print(f"✓ Addon '{args.addon_name}' installed successfully")
                return 0
            else:
                print(f"✗ Failed to install addon '{args.addon_name}'")
                return 1
        except Exception as e:
            print(f"Failed to install addon: {e}")
            return 1

    def _handle_uninstall(self, args):
        """Handle uninstall addon command"""
        try:
            manager = AddonManager()
            manager.discover_addons()

            print(f"Uninstalling addon: {args.addon_name}")

            # Check if addon exists
            addon_info = manager.get_addon_info(args.addon_name)
            if not addon_info:
                print(f"Addon '{args.addon_name}' not found")
                return 1

            # Check dependents
            if not args.force and addon_info.dependents:
                installed_dependents = []
                for dependent in addon_info.dependents:
                    dep_info = manager.get_addon_info(dependent)
                    if dep_info and dep_info.state == AddonState.INSTALLED:
                        installed_dependents.append(dependent)

                if installed_dependents:
                    print(f"Cannot uninstall '{args.addon_name}': required by {', '.join(installed_dependents)}")
                    print("Use --force to uninstall anyway")
                    return 1

            # Uninstall addon
            success = manager.uninstall_addon(args.addon_name, force=args.force)
            if success:
                print(f"✓ Addon '{args.addon_name}' uninstalled successfully")
                return 0
            else:
                print(f"✗ Failed to uninstall addon '{args.addon_name}'")
                return 1
        except Exception as e:
            print(f"Failed to uninstall addon: {e}")
            return 1

    def _handle_upgrade(self, args):
        """Handle upgrade addon command"""
        try:
            manager = AddonManager()
            manager.discover_addons()

            if args.all or not args.addon_name:
                print("Upgrading all addons...")
                # Get all addons that need upgrade
                addons_to_upgrade = manager.list_addons(AddonState.TO_UPGRADE)

                if not addons_to_upgrade:
                    print("No addons need upgrading")
                    return 0

                success_count = 0
                for addon_name in addons_to_upgrade:
                    print(f"Upgrading {addon_name}...")
                    if manager.upgrade_addon(addon_name, force=args.force):
                        success_count += 1
                    else:
                        print(f"Failed to upgrade {addon_name}")

                print(f"Successfully upgraded {success_count}/{len(addons_to_upgrade)} addons")
                return 0 if success_count == len(addons_to_upgrade) else 1
            else:
                print(f"Upgrading addon: {args.addon_name}")

                # Check if addon exists
                addon_info = manager.get_addon_info(args.addon_name)
                if not addon_info:
                    print(f"Addon '{args.addon_name}' not found")
                    return 1

                # Upgrade addon
                success = manager.upgrade_addon(args.addon_name, force=args.force)
                if success:
                    print(f"✓ Addon '{args.addon_name}' upgraded successfully")
                    return 0
                else:
                    print(f"✗ Failed to upgrade addon '{args.addon_name}'")
                    return 1
        except Exception as e:
            print(f"Failed to upgrade addon: {e}")
            return 1

    def _handle_info(self, args):
        """Handle addon info command"""
        try:
            manager = AddonManager()
            manager.discover_addons()

            addon_info = manager.get_addon_info(args.addon_name)
            if not addon_info:
                print(f"Addon '{args.addon_name}' not found")
                return 1

            manifest = addon_info.manifest
            print(f"Addon Information: {args.addon_name}")
            print("=" * 70)
            print(f"Name: {manifest.name}")
            print(f"Version: {manifest.version}")
            print(f"Author: {manifest.author}")
            print(f"Category: {manifest.category}")
            print(f"Description: {manifest.description}")
            print(f"State: {addon_info.state.value.replace('_', ' ').title()}")

            if addon_info.installed_version:
                print(f"Installed Version: {addon_info.installed_version}")
            if addon_info.available_version != addon_info.installed_version:
                print(f"Available Version: {addon_info.available_version}")
            if addon_info.install_date:
                print(f"Install Date: {addon_info.install_date.strftime('%Y-%m-%d %H:%M:%S')}")

            print(f"Dependencies: {', '.join(addon_info.dependencies) if addon_info.dependencies else 'None'}")
            print(f"Dependents: {', '.join(addon_info.dependents) if addon_info.dependents else 'None'}")
            print(f"Installable: {'Yes' if manifest.installable else 'No'}")
            print(f"Auto Install: {'Yes' if manifest.auto_install else 'No'}")
            print(f"Path: {manifest.addon_path}")

            # Show dependency tree
            print("\nDependency Tree:")
            tree = manager.get_dependency_tree(args.addon_name)
            self._print_dependency_tree(tree, indent=0)

            # Validate integrity
            is_valid, errors = manager.validate_addon_integrity(args.addon_name)
            print(f"\nIntegrity Check: {'✓ Valid' if is_valid else '✗ Invalid'}")
            if errors:
                for error in errors:
                    print(f"  - {error}")

            return 0
        except Exception as e:
            print(f"Failed to get addon info: {e}")
            return 1

    def _print_dependency_tree(self, tree, indent=0):
        """Print dependency tree recursively"""
        prefix = "  " * indent
        name = tree.get('name', 'unknown')
        state = tree.get('state', 'unknown')
        version = tree.get('version', 'unknown')

        print(f"{prefix}- {name} ({version}) [{state}]")

        if tree.get('not_found'):
            print(f"{prefix}  ⚠ Not found")
        elif tree.get('max_depth_reached'):
            print(f"{prefix}  ... (max depth reached)")
        else:
            for dep in tree.get('dependencies', []):
                self._print_dependency_tree(dep, indent + 1)

    def _handle_create_db(self, args):
        """Handle create database command"""
        print(f"Creating database: {args.db_name}")
        print("Database creation functionality not implemented yet")
        return 0

    def _handle_drop_db(self, args):
        """Handle drop database command"""
        print(f"Dropping database: {args.db_name}")
        print("Database drop functionality not implemented yet")
        return 0

    def _handle_list_db(self, args):
        """Handle list databases command"""
        print("Listing databases...")
        print("Database listing functionality not implemented yet")
        return 0

    def _handle_backup(self, args):
        """Handle database backup command"""
        print(f"Backing up database: {args.db_name}")
        print("Database backup functionality not implemented yet")
        return 0

    def _handle_restore(self, args):
        """Handle database restore command"""
        print(f"Restoring database: {args.db_name}")
        print("Database restore functionality not implemented yet")
        return 0

    def _handle_test(self, args):
        """Handle test command with comprehensive Odoo-like features"""
        try:
            # Check if test mode is enabled (Odoo-style requirement)
            if not args.test_enable:
                print("Error: Tests must be run with --test-enable flag")
                print("Example usage:")
                print("  erp-bin test --test-enable -i base")
                print("  erp-bin test --test-enable --test-tags unit")
                print("  erp-bin test --test-enable -i sale --test-tags integration")
                return 1

            # Setup test environment
            self._setup_test_environment(args)

            # Determine which framework to use
            if args.framework == 'pytest':
                return self._run_pytest_tests(args)
            elif args.framework == 'unittest':
                return self._run_unittest_tests(args)
            else:  # erp framework
                return self._run_erp_tests(args)

        except Exception as e:
            if args.verbose > 1:
                import traceback
                traceback.print_exc()
            else:
                print(f"Failed to run tests: {e}")
            return 1

    def _run_pytest_tests(self, args):
        """Run tests using pytest framework"""
        print("Running tests with pytest framework...")

        try:
            import subprocess

            # Build pytest command
            cmd = ['python', '-m', 'pytest']

            # Add module/addon selection
            test_paths = ['tests/']
            if args.modules or args.update_modules or args.module:
                # Add addon test paths
                addon_names = []
                if args.modules:
                    addon_names.extend(args.modules)
                if args.update_modules:
                    addon_names.extend(args.update_modules)
                if args.module:
                    addon_names.append(args.module)

                for addon_name in addon_names:
                    addon_test_path = f"addons/{addon_name}/tests/"
                    if os.path.exists(addon_test_path):
                        test_paths.append(addon_test_path)

            # Add test selection based on args
            if args.pattern:
                cmd.extend(['-k', args.pattern])

            # Add tag-based filtering
            if args.test_tags:
                tags = args.test_tags.replace(',', ' or ')
                cmd.extend(['-m', tags])
            elif args.unit:
                cmd.extend(['-m', 'unit'])
            elif args.integration:
                cmd.extend(['-m', 'integration'])
            elif args.performance:
                cmd.extend(['-m', 'performance'])
            elif args.async_tests:
                cmd.extend(['-m', 'async_test'])
            elif not args.all_tests:
                # Skip slow tests by default
                cmd.extend(['-m', 'not slow'])

            if args.test_exclude_tags:
                exclude_tags = args.test_exclude_tags.replace(',', ' and not ')
                cmd.extend(['-m', f'not {exclude_tags}'])

            # Add coverage options
            if args.coverage:
                cmd.extend(['--cov=erp', '--cov-report=term-missing'])

            if args.coverage_html:
                cmd.extend(['--cov=erp', '--cov-report=html'])

            # Add JUnit XML output
            if args.junit_xml:
                cmd.extend(['--junit-xml', args.junit_xml])

            # Add verbosity
            if args.quiet:
                cmd.append('-q')
            else:
                cmd.extend(['-v'] * args.verbose)

            # Add additional pytest args
            if args.pytest_args:
                cmd.extend(args.pytest_args.split())

            # Add test paths
            cmd.extend(test_paths)

            if args.list_tests:
                cmd.append('--collect-only')

            print(f"Running: {' '.join(cmd)}")

            # Run pytest
            result = subprocess.run(cmd, cwd=os.path.dirname(__file__))
            return result.returncode

        except Exception as e:
            print(f"Failed to run pytest: {e}")
            return 1

    def _run_unittest_tests(self, args):
        """Run tests using unittest framework"""
        print("Running tests with unittest framework...")

        try:
            import subprocess

            # Build unittest command
            cmd = ['python', '-m', 'unittest']

            # Add discovery options
            if args.pattern:
                cmd.extend(['discover', '-s', 'tests', '-p', f'*{args.pattern}*'])
            else:
                cmd.extend(['discover', '-s', 'tests', '-p', 'test*.py'])

            # Add verbosity
            if args.verbose > 1:
                cmd.append('-v')

            # Add additional unittest args
            if args.unittest_args:
                cmd.extend(args.unittest_args.split())

            print(f"Running: {' '.join(cmd)}")

            # Run unittest
            result = subprocess.run(cmd, cwd=os.path.dirname(__file__))
            return result.returncode

        except Exception as e:
            print(f"Failed to run unittest: {e}")
            return 1



    def _handle_test_setup(self, args):
        """Handle test setup command"""
        print("Setting up test environment...")
        print("Test setup functionality not implemented yet")
        return 0

    def _handle_test_cleanup(self, args):
        """Handle test cleanup command"""
        print("Cleaning up test environment...")
        print("Test cleanup functionality not implemented yet")
        return 0

    def _setup_test_environment(self, args):
        """Setup test environment"""
        # Set test database name
        test_db = args.test_db or 'erp_test'
        config.set('options', 'db_name', test_db)

        # Setup test configuration
        if args.verbose > 1:
            print(f"Setting up test environment with database: {test_db}")

        # Recreate database if requested
        if args.recreate_db:
            if args.verbose > 1:
                print("Recreating test database...")
            # TODO: Implement database recreation

    def _run_erp_tests(self, args):
        """Run tests using ERP testing framework"""
        try:
            from erp.tests.runner import TestRunner
            from erp.tests.tags import Tags

            if args.verbose > 1:
                print("Using ERP testing framework...")

            # Create test runner
            verbosity = 0 if args.quiet else args.verbose
            runner = TestRunner(verbosity=verbosity)

            # Determine test paths and modules
            test_paths = ['tests']
            addon_names = []

            # Handle module installation/testing
            if args.modules:
                addon_names.extend(args.modules)
            if args.update_modules:
                addon_names.extend(args.update_modules)
            if args.module:
                addon_names.append(args.module)

            # Discover tests
            if args.verbose > 1:
                print(f"Discovering tests in paths: {test_paths}")
                if addon_names:
                    print(f"Including addon tests: {addon_names}")

            runner.discover_tests(test_paths, addon_names)

            # Prepare tag filters
            include_tags = None
            exclude_tags = None

            # Handle test type filters
            if args.unit:
                include_tags = [Tags.UNIT]
            elif args.integration:
                include_tags = [Tags.INTEGRATION]
            elif args.performance:
                include_tags = [Tags.PERFORMANCE]
            elif args.async_tests:
                include_tags = ['async_test']

            # Handle explicit tag filters
            if args.test_tags:
                tag_list = [tag.strip() for tag in args.test_tags.split(',')]
                include_tags = tag_list if not include_tags else include_tags + tag_list

            if args.test_exclude_tags:
                exclude_tags = [tag.strip() for tag in args.test_exclude_tags.split(',')]

            # Add default exclusions for slow tests unless explicitly requested
            if not args.all_tests and not args.performance:
                if exclude_tags:
                    exclude_tags.append(Tags.SLOW)
                else:
                    exclude_tags = [Tags.SLOW]

            # List tests if requested
            if args.list_tests:
                test_names = runner.list_tests(include_tags, exclude_tags)
                print(f"Found {len(test_names)} tests:")
                for test_name in test_names:
                    print(f"  {test_name}")
                return 0

            # Run tests
            if args.verbose > 1:
                if include_tags:
                    print(f"Including tags: {include_tags}")
                if exclude_tags:
                    print(f"Excluding tags: {exclude_tags}")

            result = runner.run_tests(
                include_tags=include_tags,
                exclude_tags=exclude_tags,
                test_pattern=args.pattern
            )

            # Generate reports
            if args.coverage or args.coverage_html:
                print("Coverage reporting not yet implemented")

            if args.junit_xml:
                print(f"JUnit XML reporting to {args.junit_xml} not yet implemented")

            # Return appropriate exit code
            return 0 if result.wasSuccessful() else 1

        except ImportError as e:
            print(f"ERP testing framework not available: {e}")
            print("Falling back to pytest...")
            return self._run_pytest_tests(args)
        except Exception as e:
            print(f"Error running ERP tests: {e}")
            return 1


def main():
    """Main entry point"""
    cli = ERPCommandLine()
    return cli.run()


if __name__ == '__main__':
    sys.exit(main())

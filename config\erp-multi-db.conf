[options]
# Server configuration
http_port = 8069
http_interface = 127.0.0.1

# Database configuration
db_host = localhost
db_port = 5432
db_user = erp
db_password = erp

# Multi-database mode configuration
# Leave db_name empty or commented out for multi-database mode
# db_name = 

# Multi-database support
# When db_name is not set, the system works in multi-database mode
# Clients can specify database via X-Database header or ?db= query parameter
list_db = True
db_filter = ^erp_.*

# Connection pooling
db_pool_min_size = 10
db_pool_max_size = 20

# Addons path
addons_path = addons

# Logging
log_level = info
log_file = erp.log

# Security
admin_passwd = admin

# Server type (asgi or wsgi)
server_type = asgi

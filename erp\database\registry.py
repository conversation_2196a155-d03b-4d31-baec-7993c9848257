"""
Database registry for managing multiple database connections
"""
from typing import Dict, Optional, List
from .connection import DatabaseManager
from ..config import config


class DatabaseRegistry:
    """Registry for managing multiple database connections"""
    
    _databases: Dict[str, DatabaseManager] = {}
    _current_db: Optional[str] = None
    
    @classmethod
    async def get_database(cls, db_name: str) -> DatabaseManager:
        """Get database manager for given database name"""
        if db_name not in cls._databases:
            cls._databases[db_name] = DatabaseManager(db_name)
            await cls._databases[db_name].create_pool()
        return cls._databases[db_name]
    
    @classmethod
    def set_current_database(cls, db_name: str):
        """Set current active database"""
        cls._current_db = db_name
    
    @classmethod
    async def get_current_database(cls) -> Optional[DatabaseManager]:
        """Get current active database manager"""
        if cls._current_db:
            return await cls.get_database(cls._current_db)
        return None
    
    @classmethod
    async def list_databases(cls) -> List[str]:
        """List available databases"""
        # Get default database manager to query system
        default_db = await cls.get_database('postgres')
        
        try:
            query = """
                SELECT datname FROM pg_database 
                WHERE datistemplate = false 
                AND datname != 'postgres'
                ORDER BY datname
            """
            rows = await default_db.fetch(query)
            return [row['datname'] for row in rows]
        except Exception as e:
            print(f"Error listing databases: {e}")
            return []
    
    @classmethod
    async def create_database(cls, db_name: str) -> bool:
        """Create a new database"""
        # Connect to postgres database to create new database
        default_db = await cls.get_database('postgres')
        
        try:
            # Check if database already exists
            query = "SELECT 1 FROM pg_database WHERE datname = $1"
            exists = await default_db.fetchval(query, db_name)
            
            if exists:
                print(f"Database {db_name} already exists")
                return False
            
            # Create database (cannot use parameters for database name)
            query = f'CREATE DATABASE "{db_name}"'
            await default_db.execute(query)
            print(f"Database {db_name} created successfully")
            return True
            
        except Exception as e:
            print(f"Error creating database {db_name}: {e}")
            return False
    
    @classmethod
    async def drop_database(cls, db_name: str) -> bool:
        """Drop a database"""
        if db_name in ['postgres', 'template0', 'template1']:
            print(f"Cannot drop system database: {db_name}")
            return False
        
        # Close connection to the database if it exists
        if db_name in cls._databases:
            await cls._databases[db_name].close_pool()
            del cls._databases[db_name]
        
        # Connect to postgres database to drop the target database
        default_db = await cls.get_database('postgres')
        
        try:
            # Terminate connections to the target database
            terminate_query = """
                SELECT pg_terminate_backend(pid)
                FROM pg_stat_activity
                WHERE datname = $1 AND pid <> pg_backend_pid()
            """
            await default_db.execute(terminate_query, db_name)
            
            # Drop database (cannot use parameters for database name)
            query = f'DROP DATABASE IF EXISTS "{db_name}"'
            await default_db.execute(query)
            print(f"Database {db_name} dropped successfully")
            return True
            
        except Exception as e:
            print(f"Error dropping database {db_name}: {e}")
            return False
    
    @classmethod
    async def close_all(cls):
        """Close all database connections"""
        for db_name, db_manager in cls._databases.items():
            try:
                await db_manager.close_pool()
                print(f"Closed connection to database: {db_name}")
            except Exception as e:
                print(f"Error closing database {db_name}: {e}")
        
        cls._databases.clear()
        cls._current_db = None
    
    @classmethod
    async def initialize_database(cls, db_name: str) -> bool:
        """Initialize database with required tables and data"""
        try:
            db = await cls.get_database(db_name)
            
            # Create core tables if they don't exist
            await cls._create_core_tables(db)
            
            # Set as current database
            cls.set_current_database(db_name)
            
            print(f"Database {db_name} initialized successfully")
            return True
            
        except Exception as e:
            print(f"Error initializing database {db_name}: {e}")
            return False
    
    @classmethod
    async def _create_core_tables(cls, db: DatabaseManager):
        """Create core ERP tables"""
        
        # ir_module_module table
        await db.create_table('ir_module_module', {
            'id': 'VARCHAR(36) PRIMARY KEY',
            'name': 'VARCHAR(255) NOT NULL UNIQUE',
            'state': 'VARCHAR(50) DEFAULT \'uninstalled\'',
            'latest_version': 'VARCHAR(50)',
            'published_version': 'VARCHAR(50)',
            'auto_install': 'BOOLEAN DEFAULT FALSE',
            'application': 'BOOLEAN DEFAULT FALSE',
            'icon': 'TEXT',
            'summary': 'TEXT',
            'description': 'TEXT',
            'author': 'VARCHAR(255)',
            'maintainer': 'VARCHAR(255)',
            'contributors': 'TEXT',
            'website': 'VARCHAR(255)',
            'license': 'VARCHAR(50)',
            'sequence': 'INTEGER DEFAULT 100',
            'createAt': 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
            'updateAt': 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP'
        })
        
        # ir_model table
        await db.create_table('ir_model', {
            'id': 'VARCHAR(36) PRIMARY KEY',
            'name': 'VARCHAR(255) NOT NULL UNIQUE',
            'model': 'VARCHAR(255) NOT NULL UNIQUE',
            'info': 'TEXT',
            'state': 'VARCHAR(50) DEFAULT \'base\'',
            'transient': 'BOOLEAN DEFAULT FALSE',
            'createAt': 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
            'updateAt': 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP'
        })
        
        # ir_model_fields table
        await db.create_table('ir_model_fields', {
            'id': 'VARCHAR(36) PRIMARY KEY',
            'name': 'VARCHAR(255) NOT NULL',
            'complete_name': 'VARCHAR(255)',
            'model': 'VARCHAR(255) NOT NULL',
            'relation': 'VARCHAR(255)',
            'relation_field': 'VARCHAR(255)',
            'ttype': 'VARCHAR(50) NOT NULL',
            'selection': 'TEXT',
            'copy': 'BOOLEAN DEFAULT TRUE',
            'related': 'VARCHAR(255)',
            'readonly': 'BOOLEAN DEFAULT FALSE',
            'required': 'BOOLEAN DEFAULT FALSE',
            'selectable': 'BOOLEAN DEFAULT TRUE',
            'translate': 'BOOLEAN DEFAULT FALSE',
            'size': 'INTEGER',
            'state': 'VARCHAR(50) DEFAULT \'base\'',
            'on_delete': 'VARCHAR(50) DEFAULT \'set null\'',
            'domain': 'TEXT DEFAULT \'[]\'',
            'help': 'TEXT',
            'createAt': 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
            'updateAt': 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP'
        })
        
        print("Core tables created successfully")
    
    @classmethod
    def get_database_names(cls) -> List[str]:
        """Get list of currently connected database names"""
        return list(cls._databases.keys())
    
    @classmethod
    def get_current_database_name(cls) -> Optional[str]:
        """Get current database name"""
        return cls._current_db
